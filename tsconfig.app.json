{
	"compilerOptions": {
		"incremental": true,
		"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
		"target": "ES2020",
		"useDefineForClassFields": true,
		"lib": [
			"ES2020",
			"DOM",
			"DOM.Iterable"
		],
		"types": [
			"vitest/globals"
		],
		"module": "ESNext",
		"skipLibCheck": true,
		/* Bundler mode */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"isolatedModules": true,
		"moduleDetection": "force",
		"noEmit": true,
		"jsx": "react-jsx",
		/* Linting */
		"strict": true,
		"noFallthroughCasesInSwitch": true,
		"forceConsistentCasingInFileNames": true,
		/* Import paths */
		"paths": {
			"@/*": [
				"./src/*"
			],
			"@convex/*": [
				"./convex/*"
			]
		},
		"baseUrl": "."
	},
	"include": [
		"src"
	]
}