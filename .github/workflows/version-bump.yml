name: Version Bump

permissions:
  contents: write
  actions: read

on:
  push:
    branches: [master]
    paths-ignore:
      - 'README.md'
      - 'LICENSE'
      - '.gitignore'
      - 'docs/**'
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version bump type'
        required: false
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  version-bump:
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'workflow_dispatch' || !contains(github.event.head_commit.message, 'skip ci') }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Determine version bump type
        id: bump-type
        run: |
          # Check if manually triggered
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "type=${{ github.event.inputs.version_type }}" >> $GITHUB_OUTPUT
          else
            # Check commit messages for version bump indicators
            COMMIT_MSG="${{ github.event.head_commit.message }}"
            
            if [[ $COMMIT_MSG == *"BREAKING CHANGE"* ]] || [[ $COMMIT_MSG == *"[major]"* ]]; then
              echo "type=major" >> $GITHUB_OUTPUT
            elif [[ $COMMIT_MSG == *"feat"* ]] || [[ $COMMIT_MSG == *"[minor]"* ]]; then
              echo "type=minor" >> $GITHUB_OUTPUT
            else
              echo "type=patch" >> $GITHUB_OUTPUT
            fi
          fi

      - name: Bump version
        id: version
        run: |
          # Get current version
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          echo "current-version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          
          # Bump version based on type
          NEW_VERSION=$(npm version ${{ steps.bump-type.outputs.type }} --no-git-tag-version)
          echo "new-version=$NEW_VERSION" >> $GITHUB_OUTPUT
          
          # Clean the version string (remove 'v' prefix if present)
          CLEAN_VERSION=$(echo "$NEW_VERSION" | sed 's/^v//')
          echo "new-version-clean=$CLEAN_VERSION" >> $GITHUB_OUTPUT
          
          # Update deployment info in a simple way
          echo "Version bumped from $CURRENT_VERSION to $CLEAN_VERSION"

      - name: Install dependencies
        run: pnpm install

      - name: Update deployment info
        env:
          VITE_CONVEX_URL: ${{ secrets.VITE_CONVEX_URL }}
          CONVEX_DEPLOY_KEY: ${{ secrets.CONVEX_DEPLOY_KEY }}
        run: |
          # Create a simple deployment info file that can be read by the app
          cat > deployment-info.json << EOF
          {
            "version": "${{ steps.version.outputs.new-version-clean }}",
            "timestamp": $(($(date +%s) * 1000)),
            "commit": "${{ github.sha }}",
            "branch": "${{ github.ref_name }}"
          }
          EOF
          
          # Ensure Convex is deployed with latest changes
          echo "🚀 Deploying to Convex..."
          npx convex deploy --cmd-url-env-var-name VITE_CONVEX_URL || echo "Warning: Convex deployment failed"
          
          # Update deployment info in Convex database
          echo "📝 Updating deployment info in database..."
          node scripts/update-deployment-info.js || echo "Warning: Failed to update Convex deployment info"
          
          # Verify deployment info was updated
          echo "✅ Verifying deployment info..."
          npx convex run deployment:getCurrentVersion || echo "Warning: Could not verify deployment info"

      - name: Commit version bump
        run: |
          # Only commit package.json (deployment-info.json is temporary and gets cleaned up)
          git add package.json
          git commit -m "chore: bump version to ${{ steps.version.outputs.new-version-clean }} [skip ci]"
          git tag v${{ steps.version.outputs.new-version-clean }}

      - name: Push changes
        run: |
          git push origin master
          git push origin v${{ steps.version.outputs.new-version-clean }}

      - name: Get current timestamp
        id: timestamp
        run: echo "current=$(date -u +"%Y-%m-%dT%H:%M:%SZ")" >> $GITHUB_OUTPUT

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: v${{ steps.version.outputs.new-version-clean }}
          name: Release v${{ steps.version.outputs.new-version-clean }}
          body: |
            ## Changes in v${{ steps.version.outputs.new-version-clean }}
            
            **Commit:** ${{ github.sha }}
            **Branch:** ${{ github.ref_name }}
            **Timestamp:** ${{ steps.timestamp.outputs.current }}
            
            ### Commit Message
            ${{ github.event.head_commit.message || 'Manual release trigger' }}
            
            ---
            *This release was automatically generated by GitHub Actions*
          draft: false
          prerelease: false
          generate_release_notes: true