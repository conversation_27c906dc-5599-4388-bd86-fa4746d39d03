{"name": "bootleg-msn-messenger", "private": true, "version": "0.3.2", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "dev:tauri": "pnpm tauri dev", "build": "vite build", "build:netlify": "convex dev --once && vite build", "build:tauri": "pnpm tauri build", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build && biome check", "lint:b": "biome check", "lint:b:fix": "biome check --write", "version:patch": "npm version patch --no-git-tag-version && node scripts/update-deployment-info.js", "version:minor": "npm version minor --no-git-tag-version && node scripts/update-deployment-info.js", "version:major": "npm version major --no-git-tag-version && node scripts/update-deployment-info.js", "deployment:reset": "node scripts/reset-deployment-info.js", "migrate": "node scripts/run-migration.js", "test:migration": "node scripts/test-migration-integration.js", "prepare": "husky"}, "dependencies": {"@auth/core": "0.37.0", "@convex-dev/auth": "^0.0.80", "@convex-dev/resend": "^0.1.7", "@nanostores/react": "^1.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-dialog": "^2.3.2", "@tauri-apps/plugin-fs": "^2.4.1", "@tauri-apps/plugin-notification": "^2.3.0", "@tauri-apps/plugin-shell": "^2.3.0", "@tauri-apps/plugin-store": "^2.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.2", "lucide-react": "^0.536.0", "nanostores": "^1.0.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.7.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@edge-runtime/vm": "^5.0.0", "@tauri-apps/cli": "^2.7.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "convex-test": "^0.0.38", "dotenv": "^16.4.7", "globals": "^15.15.0", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^16.1.4", "npm-run-all": "^4.1.5", "tw-animate-css": "^1.3.6", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748", "lint-staged": {"*.{ts,tsx}": ["pnpm lint:b"]}}